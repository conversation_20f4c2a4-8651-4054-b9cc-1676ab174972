"""
疾病模块专用枚举定义

扩展疾病状态枚举，添加详细癌症分期、解剖位置和腺瘤分类功能。
"""

from enum import Enum
from typing import Set, Dict, List, Tuple


class CancerStage(Enum):
    """详细癌症分期枚举（I-IV期）"""
    STAGE_I = "stage_i"
    STAGE_II = "stage_ii"
    STAGE_III = "stage_iii"
    STAGE_IV = "stage_iv"

    @classmethod
    def get_early_stages(cls) -> Set["CancerStage"]:
        """获取早期癌症分期（I-II期）"""
        return {cls.STAGE_I, cls.STAGE_II}

    @classmethod
    def get_late_stages(cls) -> Set["CancerStage"]:
        """获取晚期癌症分期（III-IV期）"""
        return {cls.STAGE_III, cls.STAGE_IV}

    @classmethod
    def get_progression_order(cls) -> List["CancerStage"]:
        """获取癌症分期进展顺序"""
        return [cls.STAGE_I, cls.STAGE_II, cls.STAGE_III, cls.STAGE_IV]

    def is_early_stage(self) -> bool:
        """判断是否为早期分期"""
        return self in self.get_early_stages()

    def is_late_stage(self) -> bool:
        """判断是否为晚期分期"""
        return self in self.get_late_stages()

    def get_next_stage(self) -> "CancerStage":
        """获取下一个分期"""
        progression = self.get_progression_order()
        current_index = progression.index(self)
        if current_index < len(progression) - 1:
            return progression[current_index + 1]
        return self  # 已是最晚期


class AnatomicalLocation(Enum):
    """解剖位置枚举（近端结肠、远端结肠、直肠）"""
    PROXIMAL_COLON = "proximal_colon"    # 近端结肠
    DISTAL_COLON = "distal_colon"        # 远端结肠
    RECTUM = "rectum"                    # 直肠

    @classmethod
    def get_distribution_probabilities(cls) -> Dict["AnatomicalLocation", float]:
        """获取解剖位置分配概率"""
        return {
            cls.PROXIMAL_COLON: 0.40,  # 40%
            cls.DISTAL_COLON: 0.35,    # 35%
            cls.RECTUM: 0.25           # 25%
        }

    @classmethod
    def get_screening_sensitivity_modifiers(cls) -> Dict["AnatomicalLocation", float]:
        """获取位置特异性筛查敏感性调整因子"""
        return {
            cls.PROXIMAL_COLON: 0.8,   # 筛查敏感性较低
            cls.DISTAL_COLON: 1.0,     # 标准筛查敏感性
            cls.RECTUM: 1.2            # 筛查敏感性较高
        }

    @classmethod
    def get_progression_rate_modifiers(cls) -> Dict["AnatomicalLocation", float]:
        """获取位置特异性进展率调整因子"""
        return {
            cls.PROXIMAL_COLON: 1.1,   # 进展稍快
            cls.DISTAL_COLON: 1.0,     # 标准进展率
            cls.RECTUM: 0.9            # 进展稍慢
        }

    @classmethod
    def get_cancer_stage_distributions(cls) -> Dict["AnatomicalLocation", List[float]]:
        """获取位置特异性癌症分期分布（I-IV期）"""
        return {
            cls.PROXIMAL_COLON: [0.25, 0.35, 0.25, 0.15],  # I-IV期分布
            cls.DISTAL_COLON: [0.30, 0.40, 0.20, 0.10],
            cls.RECTUM: [0.35, 0.35, 0.20, 0.10]
        }


class AdenomaClassification:
    """精确的腺瘤分类标准
    
    低风险腺瘤：≤9mm，且无绒毛无高级别上皮内瘤变
    高风险腺瘤：≥10mm或含绒毛或高级别上皮内瘤变
    """

    # 分类阈值常量
    SIZE_THRESHOLD_MM = 10.0

    @staticmethod
    def classify_adenoma(
        size_mm: float, 
        has_villous: bool, 
        has_high_grade_dysplasia: bool
    ) -> str:
        """
        根据最新医学标准分类腺瘤
        
        Args:
            size_mm: 腺瘤大小（毫米）
            has_villous: 是否含绒毛成分
            has_high_grade_dysplasia: 是否有高级别上皮内瘤变
            
        Returns:
            str: "low_risk_adenoma" 或 "high_risk_adenoma"
        """
        if (size_mm >= AdenomaClassification.SIZE_THRESHOLD_MM or 
            has_villous or 
            has_high_grade_dysplasia):
            return "high_risk_adenoma"
        else:
            return "low_risk_adenoma"

    @staticmethod
    def get_classification_criteria() -> Dict[str, str]:
        """获取腺瘤分类标准说明"""
        return {
            "low_risk_adenoma": f"≤{AdenomaClassification.SIZE_THRESHOLD_MM-1}mm，且无绒毛无高级别上皮内瘤变",
            "high_risk_adenoma": f"≥{AdenomaClassification.SIZE_THRESHOLD_MM}mm或含绒毛或高级别上皮内瘤变"
        }


class DiseaseStateTransition:
    """疾病状态转换规则和验证"""

    @staticmethod
    def get_valid_transitions() -> Dict[str, List[str]]:
        """获取有效的疾病状态转换路径"""
        return {
            "normal": ["low_risk_adenoma"],
            "low_risk_adenoma": ["high_risk_adenoma", "normal"],  # 可能回退
            "high_risk_adenoma": ["preclinical_cancer", "low_risk_adenoma"],
            "preclinical_cancer": ["clinical_cancer_stage_i"],
            "clinical_cancer_stage_i": ["clinical_cancer_stage_ii", "death_cancer"],
            "clinical_cancer_stage_ii": ["clinical_cancer_stage_iii", "death_cancer"],
            "clinical_cancer_stage_iii": ["clinical_cancer_stage_iv", "death_cancer"],
            "clinical_cancer_stage_iv": ["death_cancer"],
            "death_cancer": [],  # 终态
            "death_other": []    # 终态
        }

    @staticmethod
    def is_valid_transition(from_state: str, to_state: str) -> bool:
        """验证状态转换是否有效"""
        valid_transitions = DiseaseStateTransition.get_valid_transitions()
        return to_state in valid_transitions.get(from_state, [])

    @staticmethod
    def get_transition_pathway() -> List[str]:
        """获取腺瘤-癌变通路的标准转换路径"""
        return [
            "normal",
            "low_risk_adenoma",
            "high_risk_adenoma",
            "preclinical_cancer",
            "clinical_cancer_stage_i",
            "clinical_cancer_stage_ii",
            "clinical_cancer_stage_iii",
            "clinical_cancer_stage_iv"
        ]


class StateTransitionHistory:
    """状态转换历史跟踪功能"""
    
    def __init__(self):
        self.transitions: List[Tuple[str, str, float]] = []  # (from_state, to_state, time)
        
    def add_transition(self, from_state: str, to_state: str, time: float) -> None:
        """添加状态转换记录"""
        if DiseaseStateTransition.is_valid_transition(from_state, to_state):
            self.transitions.append((from_state, to_state, time))
        else:
            raise ValueError(f"无效的状态转换: {from_state} -> {to_state}")
    
    def get_transition_history(self) -> List[Tuple[str, str, float]]:
        """获取完整转换历史"""
        return self.transitions.copy()
    
    def get_current_state(self) -> str:
        """获取当前状态"""
        if not self.transitions:
            return "normal"
        return self.transitions[-1][1]
    
    def get_time_in_state(self, state: str) -> float:
        """计算在特定状态的停留时间"""
        total_time = 0.0
        current_state = None
        state_start_time = 0.0
        
        for from_state, to_state, time in self.transitions:
            if current_state == state:
                total_time += time - state_start_time
            
            if to_state == state:
                state_start_time = time
            
            current_state = to_state
            
        return total_time
    
    def clear_history(self) -> None:
        """清空转换历史"""
        self.transitions.clear()
