"""
测试疾病枚举扩展功能

测试详细癌症分期、解剖位置和腺瘤分类功能。
"""

import pytest
from src.core.enums import DiseaseState, AnatomicalLocation
from src.modules.disease.enums import (
    CancerStage,
    AdenomaClassification,
    DiseaseStateTransition,
    StateTransitionHistory
)


class TestCancerStage:
    """测试癌症分期枚举"""

    def test_cancer_stage_values(self):
        """测试癌症分期值"""
        assert CancerStage.STAGE_I.value == "stage_i"
        assert CancerStage.STAGE_II.value == "stage_ii"
        assert CancerStage.STAGE_III.value == "stage_iii"
        assert CancerStage.STAGE_IV.value == "stage_iv"

    def test_early_stages(self):
        """测试早期分期识别"""
        early_stages = CancerStage.get_early_stages()
        assert CancerStage.STAGE_I in early_stages
        assert CancerStage.STAGE_II in early_stages
        assert CancerStage.STAGE_III not in early_stages
        assert CancerStage.STAGE_IV not in early_stages

    def test_late_stages(self):
        """测试晚期分期识别"""
        late_stages = CancerStage.get_late_stages()
        assert CancerStage.STAGE_III in late_stages
        assert CancerStage.STAGE_IV in late_stages
        assert CancerStage.STAGE_I not in late_stages
        assert CancerStage.STAGE_II not in late_stages

    def test_progression_order(self):
        """测试分期进展顺序"""
        progression = CancerStage.get_progression_order()
        expected = [
            CancerStage.STAGE_I,
            CancerStage.STAGE_II,
            CancerStage.STAGE_III,
            CancerStage.STAGE_IV
        ]
        assert progression == expected

    def test_is_early_stage(self):
        """测试早期分期判断"""
        assert CancerStage.STAGE_I.is_early_stage()
        assert CancerStage.STAGE_II.is_early_stage()
        assert not CancerStage.STAGE_III.is_early_stage()
        assert not CancerStage.STAGE_IV.is_early_stage()

    def test_is_late_stage(self):
        """测试晚期分期判断"""
        assert not CancerStage.STAGE_I.is_late_stage()
        assert not CancerStage.STAGE_II.is_late_stage()
        assert CancerStage.STAGE_III.is_late_stage()
        assert CancerStage.STAGE_IV.is_late_stage()

    def test_get_next_stage(self):
        """测试获取下一分期"""
        assert CancerStage.STAGE_I.get_next_stage() == CancerStage.STAGE_II
        assert CancerStage.STAGE_II.get_next_stage() == CancerStage.STAGE_III
        assert CancerStage.STAGE_III.get_next_stage() == CancerStage.STAGE_IV
        assert CancerStage.STAGE_IV.get_next_stage() == CancerStage.STAGE_IV  # 最晚期


class TestAnatomicalLocation:
    """测试解剖位置枚举"""

    def test_anatomical_location_values(self):
        """测试解剖位置值"""
        assert AnatomicalLocation.PROXIMAL_COLON.value == "proximal_colon"
        assert AnatomicalLocation.DISTAL_COLON.value == "distal_colon"
        assert AnatomicalLocation.RECTUM.value == "rectum"

    def test_distribution_probabilities(self):
        """测试位置分配概率"""
        from src.modules.disease.enums import AnatomicalLocation as DiseaseAnatomicalLocation
        probs = DiseaseAnatomicalLocation.get_distribution_probabilities()
        
        assert probs[DiseaseAnatomicalLocation.PROXIMAL_COLON] == 0.40
        assert probs[DiseaseAnatomicalLocation.DISTAL_COLON] == 0.35
        assert probs[DiseaseAnatomicalLocation.RECTUM] == 0.25
        
        # 概率总和应为1
        assert abs(sum(probs.values()) - 1.0) < 1e-10

    def test_screening_sensitivity_modifiers(self):
        """测试筛查敏感性调整因子"""
        from src.modules.disease.enums import AnatomicalLocation as DiseaseAnatomicalLocation
        modifiers = DiseaseAnatomicalLocation.get_screening_sensitivity_modifiers()
        
        assert modifiers[DiseaseAnatomicalLocation.PROXIMAL_COLON] == 0.8
        assert modifiers[DiseaseAnatomicalLocation.DISTAL_COLON] == 1.0
        assert modifiers[DiseaseAnatomicalLocation.RECTUM] == 1.2

    def test_progression_rate_modifiers(self):
        """测试进展率调整因子"""
        from src.modules.disease.enums import AnatomicalLocation as DiseaseAnatomicalLocation
        modifiers = DiseaseAnatomicalLocation.get_progression_rate_modifiers()
        
        assert modifiers[DiseaseAnatomicalLocation.PROXIMAL_COLON] == 1.1
        assert modifiers[DiseaseAnatomicalLocation.DISTAL_COLON] == 1.0
        assert modifiers[DiseaseAnatomicalLocation.RECTUM] == 0.9

    def test_cancer_stage_distributions(self):
        """测试癌症分期分布"""
        from src.modules.disease.enums import AnatomicalLocation as DiseaseAnatomicalLocation
        distributions = DiseaseAnatomicalLocation.get_cancer_stage_distributions()
        
        # 检查每个位置的分期分布
        for location, dist in distributions.items():
            assert len(dist) == 4  # I-IV期
            assert abs(sum(dist) - 1.0) < 1e-10  # 概率总和为1


class TestAdenomaClassification:
    """测试腺瘤分类功能"""

    def test_size_threshold(self):
        """测试大小阈值常量"""
        assert AdenomaClassification.SIZE_THRESHOLD_MM == 10.0

    def test_low_risk_classification(self):
        """测试低风险腺瘤分类"""
        # ≤9mm，无绒毛，无高级别上皮内瘤变
        result = AdenomaClassification.classify_adenoma(9.0, False, False)
        assert result == "low_risk_adenoma"
        
        result = AdenomaClassification.classify_adenoma(5.0, False, False)
        assert result == "low_risk_adenoma"

    def test_high_risk_classification_by_size(self):
        """测试基于大小的高风险腺瘤分类"""
        # ≥10mm
        result = AdenomaClassification.classify_adenoma(10.0, False, False)
        assert result == "high_risk_adenoma"
        
        result = AdenomaClassification.classify_adenoma(15.0, False, False)
        assert result == "high_risk_adenoma"

    def test_high_risk_classification_by_villous(self):
        """测试基于绒毛成分的高风险腺瘤分类"""
        # 含绒毛成分
        result = AdenomaClassification.classify_adenoma(8.0, True, False)
        assert result == "high_risk_adenoma"

    def test_high_risk_classification_by_dysplasia(self):
        """测试基于高级别上皮内瘤变的高风险腺瘤分类"""
        # 高级别上皮内瘤变
        result = AdenomaClassification.classify_adenoma(7.0, False, True)
        assert result == "high_risk_adenoma"

    def test_high_risk_classification_combined(self):
        """测试多因素组合的高风险腺瘤分类"""
        # 多个高风险因素
        result = AdenomaClassification.classify_adenoma(12.0, True, True)
        assert result == "high_risk_adenoma"

    def test_classification_criteria(self):
        """测试分类标准说明"""
        criteria = AdenomaClassification.get_classification_criteria()
        
        assert "low_risk_adenoma" in criteria
        assert "high_risk_adenoma" in criteria
        assert "≤9.0mm" in criteria["low_risk_adenoma"]
        assert "≥10.0mm" in criteria["high_risk_adenoma"]


class TestDiseaseStateTransition:
    """测试疾病状态转换规则"""

    def test_valid_transitions(self):
        """测试有效转换路径"""
        transitions = DiseaseStateTransition.get_valid_transitions()
        
        # 正常状态只能转换到低风险腺瘤
        assert transitions["normal"] == ["low_risk_adenoma"]
        
        # 低风险腺瘤可以进展到高风险或回退到正常
        assert "high_risk_adenoma" in transitions["low_risk_adenoma"]
        assert "normal" in transitions["low_risk_adenoma"]

    def test_is_valid_transition(self):
        """测试转换有效性验证"""
        # 有效转换
        assert DiseaseStateTransition.is_valid_transition("normal", "low_risk_adenoma")
        assert DiseaseStateTransition.is_valid_transition("low_risk_adenoma", "high_risk_adenoma")
        
        # 无效转换
        assert not DiseaseStateTransition.is_valid_transition("normal", "high_risk_adenoma")
        assert not DiseaseStateTransition.is_valid_transition("death_cancer", "normal")

    def test_transition_pathway(self):
        """测试标准转换路径"""
        pathway = DiseaseStateTransition.get_transition_pathway()
        expected = [
            "normal",
            "low_risk_adenoma",
            "high_risk_adenoma",
            "preclinical_cancer",
            "clinical_cancer_stage_i",
            "clinical_cancer_stage_ii",
            "clinical_cancer_stage_iii",
            "clinical_cancer_stage_iv"
        ]
        assert pathway == expected


class TestStateTransitionHistory:
    """测试状态转换历史跟踪"""

    def test_initialization(self):
        """测试初始化"""
        history = StateTransitionHistory()
        assert len(history.get_transition_history()) == 0
        assert history.get_current_state() == "normal"

    def test_add_valid_transition(self):
        """测试添加有效转换"""
        history = StateTransitionHistory()
        history.add_transition("normal", "low_risk_adenoma", 1.0)
        
        transitions = history.get_transition_history()
        assert len(transitions) == 1
        assert transitions[0] == ("normal", "low_risk_adenoma", 1.0)
        assert history.get_current_state() == "low_risk_adenoma"

    def test_add_invalid_transition(self):
        """测试添加无效转换"""
        history = StateTransitionHistory()
        
        with pytest.raises(ValueError, match="无效的状态转换"):
            history.add_transition("normal", "high_risk_adenoma", 1.0)

    def test_multiple_transitions(self):
        """测试多次转换"""
        history = StateTransitionHistory()
        history.add_transition("normal", "low_risk_adenoma", 1.0)
        history.add_transition("low_risk_adenoma", "high_risk_adenoma", 2.0)
        
        transitions = history.get_transition_history()
        assert len(transitions) == 2
        assert history.get_current_state() == "high_risk_adenoma"

    def test_time_in_state(self):
        """测试状态停留时间计算"""
        history = StateTransitionHistory()
        history.add_transition("normal", "low_risk_adenoma", 1.0)
        history.add_transition("low_risk_adenoma", "high_risk_adenoma", 3.0)
        history.add_transition("high_risk_adenoma", "preclinical_cancer", 5.0)
        
        # 在low_risk_adenoma状态停留了2年（3.0 - 1.0）
        time_in_low_risk = history.get_time_in_state("low_risk_adenoma")
        assert time_in_low_risk == 2.0

    def test_clear_history(self):
        """测试清空历史"""
        history = StateTransitionHistory()
        history.add_transition("normal", "low_risk_adenoma", 1.0)
        
        history.clear_history()
        assert len(history.get_transition_history()) == 0
        assert history.get_current_state() == "normal"


class TestDiseaseStateExtensions:
    """测试DiseaseState扩展功能"""

    def test_clinical_cancer_stages(self):
        """测试临床癌症分期状态"""
        clinical_stages = DiseaseState.get_clinical_cancer_stages()
        expected = {
            DiseaseState.CLINICAL_CANCER_STAGE_I,
            DiseaseState.CLINICAL_CANCER_STAGE_II,
            DiseaseState.CLINICAL_CANCER_STAGE_III,
            DiseaseState.CLINICAL_CANCER_STAGE_IV
        }
        assert clinical_stages == expected

    def test_is_clinical_cancer_stage(self):
        """测试临床癌症分期判断"""
        assert DiseaseState.CLINICAL_CANCER_STAGE_I.is_clinical_cancer_stage()
        assert DiseaseState.CLINICAL_CANCER_STAGE_II.is_clinical_cancer_stage()
        assert DiseaseState.CLINICAL_CANCER_STAGE_III.is_clinical_cancer_stage()
        assert DiseaseState.CLINICAL_CANCER_STAGE_IV.is_clinical_cancer_stage()
        
        assert not DiseaseState.NORMAL.is_clinical_cancer_stage()
        assert not DiseaseState.PRECLINICAL_CANCER.is_clinical_cancer_stage()

    def test_cancer_states_include_stages(self):
        """测试癌症状态包含所有分期"""
        cancer_states = DiseaseState.get_cancer_states()
        
        assert DiseaseState.CLINICAL_CANCER_STAGE_I in cancer_states
        assert DiseaseState.CLINICAL_CANCER_STAGE_II in cancer_states
        assert DiseaseState.CLINICAL_CANCER_STAGE_III in cancer_states
        assert DiseaseState.CLINICAL_CANCER_STAGE_IV in cancer_states
        assert DiseaseState.PRECLINICAL_CANCER in cancer_states
        assert DiseaseState.DEATH_CANCER in cancer_states
