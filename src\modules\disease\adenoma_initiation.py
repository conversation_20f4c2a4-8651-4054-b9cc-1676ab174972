"""
腺瘤产生建模引擎

实现基于年龄、性别和风险因素的乙状函数腺瘤产生概率计算。
"""

import numpy as np
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
import logging

from src.core.enums import Gender
from .risk_factors import RiskFactorProfile
from .risk_calculator import RiskCalculator

logger = logging.getLogger(__name__)


@dataclass
class SigmoidParameters:
    """乙状函数参数"""
    inflection_point: float  # 拐点年龄
    steepness: float        # 陡峭度
    max_probability: float  # 最大概率
    baseline_rate: float    # 基线率


@dataclass
class GenderMultipliers:
    """性别特异性倍数"""
    male_multiplier: float
    female_multiplier: float


class AdenomaInitiationModel:
    """腺瘤产生建模类

    使用乙状函数建模基于年龄、性别和风险因素的腺瘤产生概率。
    """

    def __init__(
        self,
        sigmoid_params: Optional[SigmoidParameters] = None,
        gender_multipliers: Optional[GenderMultipliers] = None,
        risk_calculator: Optional[RiskCalculator] = None
    ):
        """
        初始化腺瘤产生模型

        Args:
            sigmoid_params: 乙状函数参数
            gender_multipliers: 性别特异性倍数
            risk_calculator: 风险评分计算器
        """
        self.sigmoid_params = (sigmoid_params or
                               self._get_default_sigmoid_params())
        self.gender_multipliers = (gender_multipliers or
                                   self._get_default_gender_multipliers())
        self.risk_calculator = risk_calculator or RiskCalculator()

        logger.info("腺瘤产生模型初始化完成")

    def _get_default_sigmoid_params(self) -> SigmoidParameters:
        """获取默认乙状函数参数"""
        return SigmoidParameters(
            inflection_point=50.0,    # 50岁拐点
            steepness=10.0,           # 陡峭度
            max_probability=0.001,    # 最大月概率0.1%
            baseline_rate=0.00001     # 基线月概率0.001%
        )

    def _get_default_gender_multipliers(self) -> GenderMultipliers:
        """获取默认性别倍数"""
        return GenderMultipliers(
            male_multiplier=1.2,      # 男性倍数1.2
            female_multiplier=1.0     # 女性倍数1.0（基准）
        )

    def calculate_base_sigmoid_probability(self, age: float) -> float:
        """
        计算基于年龄的基础乙状函数概率

        Args:
            age: 年龄

        Returns:
            float: 基础概率（月概率）
        """
        # 乙状函数：P = baseline + (max - baseline) / (1 + exp(-(age - inflection) / steepness))
        sigmoid_component = 1 / (1 + np.exp(
            -(age - self.sigmoid_params.inflection_point) /
            self.sigmoid_params.steepness
        ))

        probability = (
            self.sigmoid_params.baseline_rate +
            (self.sigmoid_params.max_probability -
             self.sigmoid_params.baseline_rate) * sigmoid_component
        )

        return probability

    def apply_gender_adjustment(self, base_probability: float, gender: Gender) -> float:
        """
        应用性别特异性调整

        Args:
            base_probability: 基础概率
            gender: 性别

        Returns:
            float: 性别调整后的概率
        """
        if gender == Gender.MALE:
            multiplier = self.gender_multipliers.male_multiplier
        else:
            multiplier = self.gender_multipliers.female_multiplier

        return base_probability * multiplier

    def apply_risk_factor_adjustment(
        self,
        base_probability: float,
        risk_profile: RiskFactorProfile,
        age: float,
        gender: Gender
    ) -> float:
        """
        应用风险因素调整

        Args:
            base_probability: 基础概率
            risk_profile: 风险因素档案
            age: 年龄
            gender: 性别

        Returns:
            float: 风险调整后的概率
        """
        risk_score = self.risk_calculator.calculate_risk_score(
            individual_id=risk_profile.individual_id,
            risk_profile=risk_profile,
            age=age,
            gender=gender
        )

        # 使用风险评分作为倍数（风险评分通常在0.5-3.0范围）
        adjusted_probability = base_probability * risk_score.final_score

        # 确保概率不超过合理上限
        max_monthly_prob = 0.01  # 月概率上限1%
        return min(adjusted_probability, max_monthly_prob)

    def calculate_adenoma_initiation_probability(
        self,
        age: float,
        gender: Gender,
        risk_profile: RiskFactorProfile
    ) -> float:
        """
        计算腺瘤产生概率（综合所有因素）

        Args:
            age: 年龄
            gender: 性别
            risk_profile: 风险因素档案

        Returns:
            float: 月腺瘤产生概率
        """
        # 1. 计算基础年龄相关概率
        base_prob = self.calculate_base_sigmoid_probability(age)

        # 2. 应用性别调整
        gender_adjusted_prob = self.apply_gender_adjustment(base_prob, gender)

        # 3. 应用风险因素调整
        final_prob = self.apply_risk_factor_adjustment(
            gender_adjusted_prob, risk_profile, age, gender
        )

        logger.debug(
            f"腺瘤产生概率计算: 年龄={age}, 性别={gender.value}, "
            f"基础概率={base_prob:.6f}, 性别调整={gender_adjusted_prob:.6f}, "
            f"最终概率={final_prob:.6f}"
        )

        return final_prob

    def calculate_annual_probability(
        self,
        age: float,
        gender: Gender,
        risk_profile: RiskFactorProfile
    ) -> float:
        """
        计算年腺瘤产生概率

        Args:
            age: 年龄
            gender: 性别
            risk_profile: 风险因素档案

        Returns:
            float: 年腺瘤产生概率
        """
        monthly_prob = self.calculate_adenoma_initiation_probability(
            age, gender, risk_profile
        )

        # 转换为年概率：1 - (1 - monthly_prob)^12
        annual_prob = 1 - (1 - monthly_prob) ** 12

        return annual_prob

    def sample_adenoma_initiation_time(
        self,
        age: float,
        gender: Gender,
        risk_profile: RiskFactorProfile,
        max_age: float = 100.0
    ) -> Optional[float]:
        """
        随机抽样腺瘤产生时间

        Args:
            age: 当前年龄
            gender: 性别
            risk_profile: 风险因素档案
            max_age: 最大年龄

        Returns:
            Optional[float]: 腺瘤产生年龄，如果在max_age前未产生则返回None
        """
        current_age = age

        while current_age < max_age:
            # 计算当前年龄的月概率
            monthly_prob = self.calculate_adenoma_initiation_probability(
                current_age, gender, risk_profile
            )

            # 随机判断是否产生腺瘤
            if np.random.random() < monthly_prob:
                return current_age

            # 推进一个月
            current_age += 1/12

        return None  # 在最大年龄前未产生腺瘤

    def get_probability_curve(
        self,
        gender: Gender,
        risk_profile: RiskFactorProfile,
        age_range: Tuple[float, float] = (20.0, 90.0),
        age_step: float = 1.0
    ) -> Dict[float, float]:
        """
        获取概率曲线数据

        Args:
            gender: 性别
            risk_profile: 风险因素档案
            age_range: 年龄范围
            age_step: 年龄步长

        Returns:
            Dict[float, float]: 年龄到概率的映射
        """
        ages = np.arange(age_range[0], age_range[1] + age_step, age_step)
        probabilities = {}

        for age in ages:
            prob = self.calculate_annual_probability(age, gender, risk_profile)
            probabilities[float(age)] = prob

        return probabilities

    def update_sigmoid_parameters(self, new_params: SigmoidParameters) -> None:
        """
        更新乙状函数参数

        Args:
            new_params: 新的乙状函数参数
        """
        self.sigmoid_params = new_params
        logger.info(f"乙状函数参数已更新: {new_params}")

    def update_gender_multipliers(self, new_multipliers: GenderMultipliers) -> None:
        """
        更新性别倍数

        Args:
            new_multipliers: 新的性别倍数
        """
        self.gender_multipliers = new_multipliers
        logger.info(f"性别倍数已更新: {new_multipliers}")

    def get_model_parameters(self) -> Dict:
        """
        获取模型参数

        Returns:
            Dict: 模型参数字典
        """
        return {
            "sigmoid_params": {
                "inflection_point": self.sigmoid_params.inflection_point,
                "steepness": self.sigmoid_params.steepness,
                "max_probability": self.sigmoid_params.max_probability,
                "baseline_rate": self.sigmoid_params.baseline_rate
            },
            "gender_multipliers": {
                "male_multiplier": self.gender_multipliers.male_multiplier,
                "female_multiplier": self.gender_multipliers.female_multiplier
            }
        }
